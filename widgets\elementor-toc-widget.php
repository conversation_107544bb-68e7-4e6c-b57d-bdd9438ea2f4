start_controls_section(
            'content_section',
            array(
                'label' => __('Content', 'safeoid-toc'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'toc_title',
            array(
                'label' => __('TOC Title', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Table of Contents', 'safeoid-toc'),
                'placeholder' => __('Enter TOC title', 'safeoid-toc'),
            )
        );
        
        $this->add_control(
            'include_headings',
            array(
                'label' => __('Include Headings', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => array(
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                ),
                'default' => ['h2', 'h3', 'h4'],
            )
        );
        
        $this->add_control(
            'widget_position',
            array(
                'label' => __('Position', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'inline' => __('Inline (in content)', 'safeoid-toc'),
                    'floating' => __('Floating (sidebar)', 'safeoid-toc'),
                ),
                'default' => 'inline',
            )
        );
        
        $this->end_controls_section();
        
        // Style Controls
        $this->start_controls_section(
            'style_section',
            array(
                'label' => __('Style', 'safeoid-toc'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );
        
        $this->add_control(
            'background_color',
            array(
                'label' => __('Background Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'background-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'text_color',
            array(
                'label' => __('Text Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'link_color',
            array(
                'label' => __('Link Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container a' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'title_typography',
                'label' => __('Title Typography', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-title',
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'links_typography',
                'label' => __('Links Typography', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container a',
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            array(
                'name' => 'border',
                'label' => __('Border', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container',
            )
        );
        
        $this->add_control(
            'border_radius',
            array(
                'label' => __('Border Radius', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            array(
                'name' => 'box_shadow',
                'label' => __('Box Shadow', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container',
            )
        );
        
        $this->add_responsive_control(
            'padding',
            array(
                'label' => __('Padding', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );
        
        $this->end_controls_section();
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        // Get global plugin options
        $plugin_options = get_option('safeoid_toc_options', array());
        
        // Override with widget settings
        $options = array_merge($plugin_options, array(
            'headings' => $settings['include_headings'] ?? ['h2', 'h3', 'h4'],
            'label' => $settings['toc_title'] ?? __('Table of Contents', 'safeoid-toc'),
        ));
        
        // Generate TOC
        $toc_output = new SafeoidTOC_Output($options);
        $toc_data = $toc_output->get_toc_data();
        
        if (empty($toc_data['headings'])) {
            if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
                echo '';
                echo '' . __('No headings found on this page. The TOC will appear when headings are present.', 'safeoid-toc') . '';
                echo '';
            }
            return;
        }
        
        $position_class = $settings['widget_position'] === 'floating' ? 'floating-position' : 'inline-position';
        
        ?>
        
            
                
                    
                
                
                
                    generate_toc_html($toc_data['headings']); ?>
                
            
        
        
        
        
        .elementor-toc-wrapper.floating-position {
            position: fixed;
            top: 20%;
            left: 20px;
            z-index: 9999;
            max-width: 280px;
        }
        
        @media (max-width: 768px) {
            .elementor-toc-wrapper.floating-position {
                position: relative;
                top: auto;
                left: auto;
                max-width: 100%;
            }
        }
        
        
        
        
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to TOC links
            const tocLinks = document.querySelectorAll('.elementor-toc-content a[href^="#"]');
            tocLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
        
        ';
        $current_level = 0;
        
        foreach ($headings as $heading) {
            $id = SafeoidTOC_Output::generate_heading_id($heading['text']);
            $level = $heading['level'];
            
            if ($level > $current_level) {
                // Opening new nested level
                for ($i = $current_level; $i < $level - 1; $i++) {
                    $html .= '';
                }
                if ($current_level > 0) {
                    $html .= '';
                }
            } elseif ($level < $current_level) {
                // Closing nested levels
                for ($i = $level; $i < $current_level; $i++) {
                    $html .= '';
                }
            } elseif ($current_level > 0) {
                $html .= '';
            }
            
            $html .= '' . esc_html($heading['text']) . '';
            $current_level = $level;
        }
        
        // Close remaining open tags
        for ($i = 1; $i < $current_level; $i++) {
            $html .= '';
        }
        
        $html .= '';
        
        return $html;
    }
    
    protected function content_template() {
        ?>
        
            
                <# if (settings.toc_title) { #>
                    {{{ settings.toc_title }}}
                <# } #>
                
                
                    
                        
                        
                            
                                
                            
                        
                        
                    
                
            
        