<?php
/**
 * Elementor TOC Widget
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOC_Elementor_Widget extends \Elementor\Widget_Base {

    public function get_name() {
        return 'safeoid_toc';
    }

    public function get_title() {
        return __('Table of Contents', 'safeoid-toc');
    }

    public function get_icon() {
        return 'eicon-table-of-contents';
    }

    public function get_categories() {
        return ['general'];
    }

    public function get_keywords() {
        return ['toc', 'table of contents', 'navigation', 'safeoid'];
    }

    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            array(
                'label' => __('Content', 'safeoid-toc'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'toc_title',
            array(
                'label' => __('TOC Title', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Table of Contents', 'safeoid-toc'),
                'placeholder' => __('Enter TOC title', 'safeoid-toc'),
            )
        );
        
        $this->add_control(
            'include_headings',
            array(
                'label' => __('Include Headings', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => array(
                    'h1' => 'H1',
                    'h2' => 'H2',
                    'h3' => 'H3',
                    'h4' => 'H4',
                    'h5' => 'H5',
                    'h6' => 'H6',
                ),
                'default' => ['h2', 'h3', 'h4'],
            )
        );
        
        $this->add_control(
            'widget_position',
            array(
                'label' => __('Position', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'inline' => __('Inline (in content)', 'safeoid-toc'),
                    'floating' => __('Floating (sidebar)', 'safeoid-toc'),
                ),
                'default' => 'inline',
            )
        );
        
        $this->end_controls_section();
        
        // Style Controls
        $this->start_controls_section(
            'style_section',
            array(
                'label' => __('Style', 'safeoid-toc'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );
        
        $this->add_control(
            'background_color',
            array(
                'label' => __('Background Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'background-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'text_color',
            array(
                'label' => __('Text Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'link_color',
            array(
                'label' => __('Link Color', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0073aa',
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container a' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'title_typography',
                'label' => __('Title Typography', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-title',
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'links_typography',
                'label' => __('Links Typography', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container a',
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            array(
                'name' => 'border',
                'label' => __('Border', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container',
            )
        );
        
        $this->add_control(
            'border_radius',
            array(
                'label' => __('Border Radius', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            array(
                'name' => 'box_shadow',
                'label' => __('Box Shadow', 'safeoid-toc'),
                'selector' => '{{WRAPPER}} .elementor-toc-container',
            )
        );
        
        $this->add_responsive_control(
            'padding',
            array(
                'label' => __('Padding', 'safeoid-toc'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => array(
                    '{{WRAPPER}} .elementor-toc-container' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ),
            )
        );
        
        $this->end_controls_section();
    }
    
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        // Get global plugin options
        $plugin_options = get_option('safeoid_toc_options', array());
        
        // Override with widget settings
        $options = array_merge($plugin_options, array(
            'headings' => $settings['include_headings'] ?? ['h2', 'h3', 'h4'],
            'label' => $settings['toc_title'] ?? __('Table of Contents', 'safeoid-toc'),
        ));
        
        // Generate TOC
        $toc_output = new SafeoidTOC_Output($options);
        $toc_data = $toc_output->get_toc_data();
        
        if (empty($toc_data['headings'])) {
            if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
                echo '';
                echo '' . __('No headings found on this page. The TOC will appear when headings are present.', 'safeoid-toc') . '';
                echo '';
            }
            return;
        }
        
        $position_class = $settings['widget_position'] === 'floating' ? 'floating-position' : 'inline-position';
        
        ?>
        <div class="elementor-toc-wrapper <?php echo esc_attr($position_class); ?>">
            <div class="elementor-toc-container">
                <h3 class="elementor-toc-title">
                    <?php echo esc_html($settings['toc_title']); ?>
                </h3>
                <div class="elementor-toc-content">
                    <?php echo $this->generate_toc_html($toc_data['headings']); ?>
                </div>
            </div>
        </div>

        <style>
        .elementor-toc-wrapper.floating-position {
            position: fixed;
            top: 20%;
            left: 20px;
            z-index: 9999;
            max-width: 280px;
        }

        @media (max-width: 768px) {
            .elementor-toc-wrapper.floating-position {
                position: relative;
                top: auto;
                left: auto;
                max-width: 100%;
            }
        }
        </style>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to TOC links
            const tocLinks = document.querySelectorAll('.elementor-toc-content a[href^="#"]');
            tocLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
        </script>
        <?php;
    private function generate_toc_html($headings) {
        $html = '<ul class="elementor-toc-list">';
        $current_level = 0;

        foreach ($headings as $heading) {
            $id = SafeoidTOC_Output::generate_heading_id($heading['text']);
            $level = $heading['level'];

            if ($level > $current_level) {
                // Opening new nested level
                for ($i = $current_level; $i < $level - 1; $i++) {
                    $html .= '<ul>';
                }
                if ($current_level > 0) {
                    $html .= '<ul>';
                }
            } elseif ($level < $current_level) {
                // Closing nested levels
                for ($i = $level; $i < $current_level; $i++) {
                    $html .= '</ul>';
                }
            } elseif ($current_level > 0) {
                $html .= '</li>';
            }

            $html .= '<li><a href="#' . esc_attr($id) . '">' . esc_html($heading['text']) . '</a>';
            $current_level = $level;
        }

        // Close remaining open tags
        for ($i = 1; $i < $current_level; $i++) {
            $html .= '</ul>';
        }

        $html .= '</li></ul>';
        
        return $html;
    }
    
    protected function content_template() {
        ?>
        <div class="elementor-toc-wrapper">
            <div class="elementor-toc-container">
                <# if (settings.toc_title) { #>
                    <h3 class="elementor-toc-title">{{{ settings.toc_title }}}</h3>
                <# } #>
                <div class="elementor-toc-content">
                    <ul class="elementor-toc-list">
                        <li>
                            <a href="#"><?php _e('Sample Heading 1', 'safeoid-toc'); ?></a>
                            <ul>
                                <li>
                                    <a href="#"><?php _e('Sample Heading 2', 'safeoid-toc'); ?></a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <?php
    }
}