/**
 * Safeoid TOC JavaScript
 */

(function($) {
    'use strict';

    class SafeoidTOC {
        constructor() {
            this.init();
        }

        init() {
            this.generateTOC();
            this.bindEvents();
            this.handleMobileToggle();
            this.highlightActiveSection();
            this.addHeadingIds();
        }

        generateTOC() {
            const tocContainer = document.getElementById('toc-list');
            if (!tocContainer) return;

            const headings = this.getHeadings();
            if (headings.length === 0) {
                document.getElementById('safeoid-toc').style.display = 'none';
                return;
            }

            const tocHTML = this.buildTOCHTML(headings);
            tocContainer.innerHTML = tocHTML;

            // Add body class for styling
            document.body.classList.add('has-toc');
        }

        getHeadings() {
            const allowedHeadings = safeoidTocOptions.headings || ['h2', 'h3', 'h4'];
            const selector = allowedHeadings.join(', ');
            const headings = document.querySelectorAll(selector);

            return Array.from(headings).map(heading => ({
                element: heading,
                text: heading.textContent.trim(),
                level: parseInt(heading.tagName.substring(1)),
                id: this.generateId(heading.textContent.trim())
            }));
        }

        buildTOCHTML(headings) {
            let html = '';
            let currentLevel = 0;

            headings.forEach((heading, index) => {
                const level = heading.level;

                if (level > currentLevel) {
                    // Opening new nested level
                    for (let i = currentLevel; i < level - 1; i++) {
                        html += '<ul>';
                    }
                    if (currentLevel > 0) {
                        html += '<ul>';
                    }
                } else if (level < currentLevel) {
                    // Closing nested levels
                    for (let i = level; i < currentLevel; i++) {
                        html += '</ul>';
                    }
                } else if (currentLevel > 0) {
                    html += '</li>';
                }

                html += `<li><a href="#${heading.id}" data-target="${heading.id}">${heading.text}</a>`;
                currentLevel = level;
            });

            // Close remaining open tags
            for (let i = 1; i < currentLevel; i++) {
                html += '</ul>';
            }
            html += '</li>';

            return html;
        }

        generateId(text) {
            return text.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-')
                .substring(0, 50);
        }

        addHeadingIds() {
            const headings = this.getHeadings();
            headings.forEach(heading => {
                if (!heading.element.id) {
                    heading.element.id = heading.id;
                }
            });
        }

        bindEvents() {
            // Smooth scrolling for TOC links
            document.addEventListener('click', (e) => {
                if (e.target.matches('.toc-content a[href^="#"]')) {
                    e.preventDefault();
                    const targetId = e.target.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // Update active link
                        this.updateActiveLink(e.target);
                    }
                }
            });

            // Scroll spy
            window.addEventListener('scroll', () => {
                this.throttle(this.updateActiveOnScroll.bind(this), 100)();
            });
        }

        handleMobileToggle() {
            const toggle = document.querySelector('.toc-toggle');
            const content = document.querySelector('.toc-content');

            if (toggle && content) {
                toggle.addEventListener('click', () => {
                    content.classList.toggle('active');
                });
            }
        }

        updateActiveLink(activeLink) {
            // Remove active class from all links
            document.querySelectorAll('.toc-content a').forEach(link => {
                link.classList.remove('active');
            });

            // Add active class to clicked link
            activeLink.classList.add('active');
        }

        updateActiveOnScroll() {
            const headings = this.getHeadings();
            const scrollPosition = window.scrollY + 100; // Offset for better UX

            let activeHeading = null;

            headings.forEach(heading => {
                const element = heading.element;
                const offsetTop = element.offsetTop;

                if (scrollPosition >= offsetTop) {
                    activeHeading = heading;
                }
            });

            if (activeHeading) {
                const activeLink = document.querySelector(`a[data-target="${activeHeading.id}"]`);
                if (activeLink) {
                    this.updateActiveLink(activeLink);
                }
            }
        }

        highlightActiveSection() {
            // Initial highlight on page load
            setTimeout(() => {
                this.updateActiveOnScroll();
            }, 100);
        }

        throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof safeoidTocOptions !== 'undefined') {
            new SafeoidTOC();
        }
    });

})(jQuery);