options = get_option('safeoid_toc_options', array(
            'headings' => array('h2', 'h3', 'h4'),
            'label' => __('Table of Contents', 'safeoid-toc'),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#333333',
            'link_color' => '#0073aa',
            'bg_color' => '#ffffff',
            'enable_schema' => true
        ));
        
        // Hook into WordPress
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'output_toc'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        
        // Load includes
        $this->load_includes();
        
        // Initialize Elementor widget
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget'));
    }
    
    public function load_includes() {
        require_once SAFEOID_TOC_PLUGIN_PATH . 'inc/toc-output.php';
        require_once SAFEOID_TOC_PLUGIN_PATH . 'inc/settings-page.php';
        require_once SAFEOID_TOC_PLUGIN_PATH . 'inc/schema.php';
    }
    
    public function enqueue_scripts() {
        if (!is_admin() && (is_single() || is_page())) {
            wp_enqueue_style(
                'safeoid-toc-style',
                SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
                array(),
                SAFEOID_TOC_VERSION
            );
            
            wp_enqueue_script(
                'safeoid-toc-script',
                SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
                array(),
                SAFEOID_TOC_VERSION,
                true
            );
            
            // Pass options to JavaScript
            wp_localize_script('safeoid-toc-script', 'safeoidTocOptions', array(
                'headings' => $this->options['headings'],
                'label' => $this->options['label'],
                'enableDesktop' => $this->options['enable_desktop'],
                'enableMobile' => $this->options['enable_mobile']
            ));
        }
    }
    
    public function output_toc() {
        if (!is_admin() && (is_single() || is_page())) {
            $toc_output = new SafeoidTOC_Output($this->options);
            $toc_output->render();
            
            // Output schema if enabled
            if ($this->options['enable_schema']) {
                $schema = new SafeoidTOC_Schema($this->options);
                $schema->output_schema();
            }
        }
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('TOC Settings', 'safeoid-toc'),
            __('TOC Settings', 'safeoid-toc'),
            'manage_options',
            'safeoid-toc',
            array($this, 'admin_page'),
            'dashicons-list-view',
            30
        );
    }
    
    public function admin_init() {
        register_setting('safeoid_toc_options', 'safeoid_toc_options');
    }
    
    public function admin_page() {
        $settings_page = new SafeoidTOC_Settings($this->options);
        $settings_page->render();
    }
    
    public function register_elementor_widget() {
        require_once SAFEOID_TOC_PLUGIN_PATH . 'widgets/elementor-toc-widget.php';
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \SafeoidTOC_Elementor_Widget());
    }
    
    public function get_options() {
        return $this->options;
    }
}

// Initialize the plugin
new SafeoidTOC();

// Activation hook
register_activation_hook(__FILE__, 'safeoid_toc_activate');
function safeoid_toc_activate() {
    // Set default options
    $default_options = array(
        'headings' => array('h2', 'h3', 'h4'),
        'label' => __('Table of Contents', 'safeoid-toc'),
        'enable_desktop' => true,
        'enable_mobile' => true,
        'text_color' => '#333333',
        'link_color' => '#0073aa',
        'bg_color' => '#ffffff',
        'enable_schema' => true
    );
    
    if (!get_option('safeoid_toc_options')) {
        add_option('safeoid_toc_options', $default_options);
    }
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'safeoid_toc_deactivate');
function safeoid_toc_deactivate() {
    // Clean up temporary data if any
    wp_cache_flush();
}
?>