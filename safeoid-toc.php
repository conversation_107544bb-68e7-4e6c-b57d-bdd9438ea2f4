<?php
/**
 * Plugin Name: Safeoid TOC
 * Plugin URI: https://safeoid.com/
 * Description: A professional Table of Contents plugin with floating sidebar, Elementor widget, and schema markup support.
 * Version: 1.0.0
 * Author: Safeoid
 * Author URI: https://safeoid.com/
 * Text Domain: safeoid-toc
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SAFEOID_TOC_VERSION', '1.0.0');
define('SAFEOID_TOC_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('SAFEOID_TOC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SAFEOID_TOC_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class SafeoidTOC {

    private $options;

    public function __construct() {
        error_log('Safeoid TOC: Constructor called');

        // Set default options
        $default_options = array(
            'headings' => array('h2', 'h3', 'h4'),
            'label' => __('Table of Contents', 'safeoid-toc'),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#333333',
            'link_color' => '#0073aa',
            'bg_color' => '#ffffff',
            'enable_schema' => true
        );

        // Get saved options or use defaults
        $this->options = get_option('safeoid_toc_options', $default_options);
        error_log('Safeoid TOC: Options loaded: ' . print_r($this->options, true));

        // Load includes first to ensure classes are available
        $this->load_includes();

        // Hook into WordPress
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'output_toc'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));

        // Initialize Elementor widget - support both old and new hooks
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget')); // Old hook
        add_action('elementor/widgets/register', array($this, 'register_elementor_widget')); // New hook

        error_log('Safeoid TOC: Constructor completed, hooks registered');

        // Add admin notice to confirm plugin is loaded
        add_action('admin_notices', array($this, 'admin_notice_plugin_loaded'));

        // Add plugin action links
        add_filter('plugin_action_links_' . plugin_basename(SAFEOID_TOC_PLUGIN_FILE), array($this, 'plugin_action_links'));

        // Add dashboard widget
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widget'));

        // Add shortcode support
        add_shortcode('safeoid_toc', array($this, 'shortcode_handler'));
    }

    public function admin_notice_plugin_loaded() {
        // Only show once per session
        if (!get_transient('safeoid_toc_loaded_notice')) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Safeoid TOC:</strong> Plugin loaded successfully! Check the admin menu or Settings > Safeoid TOC.</p>';
            echo '</div>';
            set_transient('safeoid_toc_loaded_notice', true, 60); // Show for 1 minute
        }
    }

    public function plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=safeoid-toc') . '">' . __('Settings', 'safeoid-toc') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'safeoid_toc_dashboard_widget',
            __('Safeoid TOC Status', 'safeoid-toc'),
            array($this, 'dashboard_widget_content')
        );
    }

    public function dashboard_widget_content() {
        echo '<p>' . __('Table of Contents plugin is active and ready to use.', 'safeoid-toc') . '</p>';
        echo '<p><a href="' . admin_url('admin.php?page=safeoid-toc') . '" class="button button-primary">' . __('Configure Settings', 'safeoid-toc') . '</a></p>';
        echo '<p><small>' . __('The TOC will automatically appear on posts and pages with headings.', 'safeoid-toc') . '</small></p>';
        echo '<p><small>' . __('You can also use the shortcode: [safeoid_toc]', 'safeoid-toc') . '</small></p>';
    }

    public function shortcode_handler($atts) {
        // Parse attributes
        $atts = shortcode_atts(array(
            'title' => $this->options['label'],
            'headings' => implode(',', $this->options['headings']),
        ), $atts, 'safeoid_toc');

        // Convert headings to array
        $headings = explode(',', $atts['headings']);
        $headings = array_map('trim', $headings);

        // Create custom options for this shortcode
        $custom_options = $this->options;
        $custom_options['label'] = $atts['title'];
        $custom_options['headings'] = $headings;

        // Check if SafeoidTOC_Output class exists
        if (!class_exists('SafeoidTOC_Output')) {
            return '<div class="safeoid-toc-error">' . __('TOC Output class not found.', 'safeoid-toc') . '</div>';
        }

        // Start output buffering
        ob_start();

        // Generate TOC
        $toc_output = new SafeoidTOC_Output($custom_options);
        $toc_output->render();

        // Get the output
        $output = ob_get_clean();

        return $output;
    }
    
    public function load_includes() {
        $includes = array(
            'inc/toc-output.php',
            'inc/settings-page.php',
            'inc/schema.php'
        );

        foreach ($includes as $file) {
            $file_path = SAFEOID_TOC_PLUGIN_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                error_log('Safeoid TOC: Missing file - ' . $file_path);
            }
        }
    }
    
    public function enqueue_scripts() {
        if (!is_admin() && (is_single() || is_page())) {
            wp_enqueue_style(
                'safeoid-toc-style',
                SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
                array(),
                SAFEOID_TOC_VERSION
            );
            
            wp_enqueue_script(
                'safeoid-toc-script',
                SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
                array('jquery'),
                SAFEOID_TOC_VERSION,
                true
            );
            
            // Pass options to JavaScript
            wp_localize_script('safeoid-toc-script', 'safeoidTocOptions', array(
                'headings' => $this->options['headings'],
                'label' => $this->options['label'],
                'enableDesktop' => $this->options['enable_desktop'],
                'enableMobile' => $this->options['enable_mobile']
            ));
        }
    }
    
    public function output_toc() {
        // Only run on frontend single posts/pages
        if (!is_admin() && (is_single() || is_page())) {
            // Check if the class exists before instantiating
            if (class_exists('SafeoidTOC_Output')) {
                $toc_output = new SafeoidTOC_Output($this->options);
                $toc_output->render();

                // Output schema if enabled
                if ($this->options['enable_schema'] && class_exists('SafeoidTOC_Schema')) {
                    $schema = new SafeoidTOC_Schema($this->options);
                    $schema->output_schema();
                }
            } else {
                error_log('Safeoid TOC: SafeoidTOC_Output class not found');
            }
        }
    }
    
    public function add_admin_menu() {
        // Add main menu page
        $hook = add_menu_page(
            __('TOC Settings', 'safeoid-toc'),
            __('Safeoid TOC', 'safeoid-toc'),
            'manage_options',
            'safeoid-toc',
            array($this, 'admin_page'),
            'dashicons-list-view',
            30
        );

        // Add submenu page for better visibility
        add_submenu_page(
            'safeoid-toc',
            __('TOC Settings', 'safeoid-toc'),
            __('Settings', 'safeoid-toc'),
            'manage_options',
            'safeoid-toc',
            array($this, 'admin_page')
        );

        // Also add under Settings menu for easier access
        add_options_page(
            __('Safeoid TOC Settings', 'safeoid-toc'),
            __('Safeoid TOC', 'safeoid-toc'),
            'manage_options',
            'safeoid-toc-settings',
            array($this, 'admin_page')
        );

        // Log successful menu registration
        if ($hook) {
            error_log('Safeoid TOC: Admin menu registered successfully with hook: ' . $hook);
        } else {
            error_log('Safeoid TOC: Failed to register admin menu');
        }
    }
    
    public function admin_init() {
        register_setting('safeoid_toc_options', 'safeoid_toc_options');
    }
    
    public function admin_page() {
        error_log('Safeoid TOC: Admin page called');

        if (class_exists('SafeoidTOC_Settings')) {
            error_log('Safeoid TOC: SafeoidTOC_Settings class found, rendering page');
            $settings_page = new SafeoidTOC_Settings($this->options);
            $settings_page->render();
        } else {
            error_log('Safeoid TOC: SafeoidTOC_Settings class not found');
            echo '<div class="wrap">';
            echo '<h1>' . __('Safeoid TOC Settings', 'safeoid-toc') . '</h1>';
            echo '<div class="notice notice-error"><p>Settings class not found. Please check plugin installation.</p></div>';
            echo '<p>Debug Info:</p>';
            echo '<ul>';
            echo '<li>Plugin Path: ' . SAFEOID_TOC_PLUGIN_PATH . '</li>';
            echo '<li>Settings File: ' . (file_exists(SAFEOID_TOC_PLUGIN_PATH . 'inc/settings-page.php') ? 'EXISTS' : 'MISSING') . '</li>';
            echo '<li>Current User Can Manage Options: ' . (current_user_can('manage_options') ? 'YES' : 'NO') . '</li>';
            echo '</ul>';
            echo '</div>';
        }
    }
    
    public function register_elementor_widget($widgets_manager = null) {
        error_log('Safeoid TOC: Elementor widget registration called');

        // Check if Elementor is installed and activated
        if (!did_action('elementor/loaded')) {
            error_log('Safeoid TOC: Elementor not loaded');
            return;
        }

        // Check if the widget file exists
        $widget_file = SAFEOID_TOC_PLUGIN_PATH . 'widgets/elementor-toc-widget.php';
        if (!file_exists($widget_file)) {
            error_log('Safeoid TOC: Elementor widget file not found: ' . $widget_file);
            return;
        }

        require_once $widget_file;

        if (!class_exists('\SafeoidTOC_Elementor_Widget')) {
            error_log('Safeoid TOC: SafeoidTOC_Elementor_Widget class not found');
            return;
        }

        error_log('Safeoid TOC: Attempting to register Elementor widget');

        // Support both old and new Elementor versions
        try {
            if ($widgets_manager) {
                // New Elementor (3.5+)
                $widgets_manager->register(new \SafeoidTOC_Elementor_Widget());
                error_log('Safeoid TOC: Registered widget with new method');
            } else {
                // Old Elementor
                \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \SafeoidTOC_Elementor_Widget());
                error_log('Safeoid TOC: Registered widget with old method');
            }
        } catch (Exception $e) {
            error_log('Safeoid TOC: Error registering Elementor widget: ' . $e->getMessage());

            // Fallback attempt
            try {
                \Elementor\Plugin::instance()->widgets_manager->register(new \SafeoidTOC_Elementor_Widget());
                error_log('Safeoid TOC: Registered widget with fallback method');
            } catch (Exception $e) {
                error_log('Safeoid TOC: Fallback registration also failed: ' . $e->getMessage());
            }
        }
    }
    
    public function get_options() {
        return $this->options;
    }
}

// Initialize the plugin
function safeoid_toc_init() {
    // Check if WordPress is properly loaded
    if (!function_exists('add_action') || !function_exists('get_option')) {
        error_log('Safeoid TOC: WordPress functions not available');
        return;
    }

    try {
        // Check if required files exist
        $required_files = array(
            SAFEOID_TOC_PLUGIN_PATH . 'inc/toc-output.php',
            SAFEOID_TOC_PLUGIN_PATH . 'inc/settings-page.php',
            SAFEOID_TOC_PLUGIN_PATH . 'inc/schema.php'
        );

        foreach ($required_files as $file) {
            if (!file_exists($file)) {
                throw new Exception('Required file missing: ' . $file);
            }
        }

        new SafeoidTOC();
        error_log('Safeoid TOC: Plugin initialized successfully');

    } catch (Exception $e) {
        error_log('Safeoid TOC initialization error: ' . $e->getMessage());
        error_log('Safeoid TOC error trace: ' . $e->getTraceAsString());

        if (is_admin()) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p><strong>Safeoid TOC Error:</strong> ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    } catch (Error $e) {
        error_log('Safeoid TOC fatal error: ' . $e->getMessage());
        error_log('Safeoid TOC error trace: ' . $e->getTraceAsString());

        if (is_admin()) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p><strong>Safeoid TOC Fatal Error:</strong> ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }
}

// Initialize after plugins are loaded
add_action('plugins_loaded', 'safeoid_toc_init');

// Debug function to check plugin status
function safeoid_toc_debug_info() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $debug_info = array(
        'Plugin Path' => SAFEOID_TOC_PLUGIN_PATH,
        'Plugin URL' => SAFEOID_TOC_PLUGIN_URL,
        'WordPress Version' => get_bloginfo('version'),
        'PHP Version' => PHP_VERSION,
        'Required Files' => array()
    );

    $required_files = array(
        'inc/toc-output.php',
        'inc/settings-page.php',
        'inc/schema.php',
        'widgets/elementor-toc-widget.php',
        'js/toc.js',
        'css/style.css'
    );

    foreach ($required_files as $file) {
        $full_path = SAFEOID_TOC_PLUGIN_PATH . $file;
        $debug_info['Required Files'][$file] = file_exists($full_path) ? 'EXISTS' : 'MISSING';
    }

    error_log('Safeoid TOC Debug Info: ' . print_r($debug_info, true));
}

// Add debug info on admin init
add_action('admin_init', 'safeoid_toc_debug_info');

// Activation hook
register_activation_hook(__FILE__, 'safeoid_toc_activate');
function safeoid_toc_activate() {
    // Check if WordPress functions are available
    if (!function_exists('get_option') || !function_exists('add_option')) {
        return;
    }

    // Set default options
    $default_options = array(
        'headings' => array('h2', 'h3', 'h4'),
        'label' => 'Table of Contents', // Don't use __() in activation hook
        'enable_desktop' => true,
        'enable_mobile' => true,
        'text_color' => '#333333',
        'link_color' => '#0073aa',
        'bg_color' => '#ffffff',
        'enable_schema' => true
    );

    // Only add options if they don't exist
    if (!get_option('safeoid_toc_options')) {
        add_option('safeoid_toc_options', $default_options);
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'safeoid_toc_deactivate');
function safeoid_toc_deactivate() {
    // Check if WordPress functions are available
    if (!function_exists('wp_cache_flush') || !function_exists('flush_rewrite_rules')) {
        return;
    }

    // Clean up temporary data if any
    wp_cache_flush();
    flush_rewrite_rules();
}
?>