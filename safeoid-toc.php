<?php
/**
 * Plugin Name: Safeoid TOC
 * Plugin URI: https://safeoid.com/
 * Description: A professional Table of Contents plugin with floating sidebar, Elementor widget, and schema markup support.
 * Version: 1.0.0
 * Author: Safeoid
 * Author URI: https://safeoid.com/
 * Text Domain: safeoid-toc
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SAFEOID_TOC_VERSION', '1.0.0');
define('SAFEOID_TOC_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('SAFEOID_TOC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SAFEOID_TOC_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class SafeoidTOC {

    private $options;

    public function __construct() {
        // Set default options
        $default_options = array(
            'headings' => array('h2', 'h3', 'h4'),
            'label' => __('Table of Contents', 'safeoid-toc'),
            'enable_desktop' => true,
            'enable_mobile' => true,
            'text_color' => '#333333',
            'link_color' => '#0073aa',
            'bg_color' => '#ffffff',
            'enable_schema' => true
        );

        // Get saved options or use defaults
        $this->options = get_option('safeoid_toc_options', $default_options);

        // Load includes first to ensure classes are available
        $this->load_includes();

        // Hook into WordPress
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'output_toc'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));

        // Initialize Elementor widget (only if Elementor is active)
        if (did_action('elementor/loaded')) {
            add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget'));
        }
    }
    
    public function load_includes() {
        $includes = array(
            'inc/toc-output.php',
            'inc/settings-page.php',
            'inc/schema.php'
        );

        foreach ($includes as $file) {
            $file_path = SAFEOID_TOC_PLUGIN_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                error_log('Safeoid TOC: Missing file - ' . $file_path);
            }
        }
    }
    
    public function enqueue_scripts() {
        if (!is_admin() && (is_single() || is_page())) {
            wp_enqueue_style(
                'safeoid-toc-style',
                SAFEOID_TOC_PLUGIN_URL . 'css/style.css',
                array(),
                SAFEOID_TOC_VERSION
            );
            
            wp_enqueue_script(
                'safeoid-toc-script',
                SAFEOID_TOC_PLUGIN_URL . 'js/toc.js',
                array(),
                SAFEOID_TOC_VERSION,
                true
            );
            
            // Pass options to JavaScript
            wp_localize_script('safeoid-toc-script', 'safeoidTocOptions', array(
                'headings' => $this->options['headings'],
                'label' => $this->options['label'],
                'enableDesktop' => $this->options['enable_desktop'],
                'enableMobile' => $this->options['enable_mobile']
            ));
        }
    }
    
    public function output_toc() {
        // Only run on frontend single posts/pages
        if (!is_admin() && (is_single() || is_page())) {
            // Check if the class exists before instantiating
            if (class_exists('SafeoidTOC_Output')) {
                $toc_output = new SafeoidTOC_Output($this->options);
                $toc_output->render();

                // Output schema if enabled
                if ($this->options['enable_schema'] && class_exists('SafeoidTOC_Schema')) {
                    $schema = new SafeoidTOC_Schema($this->options);
                    $schema->output_schema();
                }
            } else {
                error_log('Safeoid TOC: SafeoidTOC_Output class not found');
            }
        }
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('TOC Settings', 'safeoid-toc'),
            __('TOC Settings', 'safeoid-toc'),
            'manage_options',
            'safeoid-toc',
            array($this, 'admin_page'),
            'dashicons-list-view',
            30
        );
    }
    
    public function admin_init() {
        register_setting('safeoid_toc_options', 'safeoid_toc_options');
    }
    
    public function admin_page() {
        if (class_exists('SafeoidTOC_Settings')) {
            $settings_page = new SafeoidTOC_Settings($this->options);
            $settings_page->render();
        } else {
            echo '<div class="wrap"><h1>TOC Settings</h1><p>Settings class not found. Please check plugin installation.</p></div>';
        }
    }
    
    public function register_elementor_widget() {
        // Check if Elementor is installed and activated
        if (!did_action('elementor/loaded')) {
            return;
        }

        require_once SAFEOID_TOC_PLUGIN_PATH . 'widgets/elementor-toc-widget.php';
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \SafeoidTOC_Elementor_Widget());
    }
    
    public function get_options() {
        return $this->options;
    }
}

// Initialize the plugin
function safeoid_toc_init() {
    // Check if WordPress is properly loaded
    if (!function_exists('add_action') || !function_exists('get_option')) {
        return;
    }

    try {
        new SafeoidTOC();
    } catch (Exception $e) {
        error_log('Safeoid TOC initialization error: ' . $e->getMessage());
        if (is_admin()) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>Safeoid TOC Error: ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }
}

// Initialize after plugins are loaded
add_action('plugins_loaded', 'safeoid_toc_init');

// Activation hook
register_activation_hook(__FILE__, 'safeoid_toc_activate');
function safeoid_toc_activate() {
    // Set default options
    $default_options = array(
        'headings' => array('h2', 'h3', 'h4'),
        'label' => __('Table of Contents', 'safeoid-toc'),
        'enable_desktop' => true,
        'enable_mobile' => true,
        'text_color' => '#333333',
        'link_color' => '#0073aa',
        'bg_color' => '#ffffff',
        'enable_schema' => true
    );
    
    if (!get_option('safeoid_toc_options')) {
        add_option('safeoid_toc_options', $default_options);
    }
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'safeoid_toc_deactivate');
function safeoid_toc_deactivate() {
    // Clean up temporary data if any
    wp_cache_flush();
}
?>