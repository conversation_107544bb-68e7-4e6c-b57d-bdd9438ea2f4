<?php
/**
 * TOC Output Handler
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOC_Output {

    private $options;

    public function __construct($options) {
        $this->options = $options;
    }
    
    public function render() {
        $content = $this->get_post_content();
        
        if (empty($content)) {
            return;
        }
        
        $headings = $this->extract_headings($content);
        
        if (empty($headings)) {
            return;
        }
        
        $this->output_toc_html();
        $this->output_custom_css();
    }
    
    private function get_post_content() {
        global $post;
        
        if (!$post || empty($post->post_content)) {
            return '';
        }
        
        // Apply content filters to get the same content as displayed
        $content = apply_filters('the_content', $post->post_content);
        return $content;
    }
    
    private function extract_headings($content) {
        $headings = array();
        $allowed_headings = $this->options['headings'];
        
        if (empty($allowed_headings)) {
            return $headings;
        }
        
        // Create regex pattern for selected headings
        $heading_pattern = '<(' . implode('|', $allowed_headings) . ')[^>]*>(.*?)';
        
        if (preg_match_all('/' . $heading_pattern . '/i', $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $tag = strtolower($match[1]);
                $text = strip_tags($match[2]);
                $text = trim($text);
                
                if (!empty($text)) {
                    $headings[] = array(
                        'tag' => $tag,
                        'text' => $text,
                        'level' => (int) substr($tag, 1)
                    );
                }
            }
        }
        
        return $headings;
    }
    
    private function output_toc_html() {
        $label = esc_html($this->options['label']);

        echo '<div class="floating-toc" id="safeoid-toc">';
        echo '<h3 class="toc-heading">' . $label . '</h3>';
        echo '<div class="toc-toggle">';
        echo '☰ ' . $label;
        echo '</div>';
        echo '<div class="toc-content">';
        echo '<ul id="toc-list">';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
    }
    
    private function output_custom_css() {
        $text_color = sanitize_hex_color($this->options['text_color']);
        $link_color = sanitize_hex_color($this->options['link_color']);
        $bg_color = sanitize_hex_color($this->options['bg_color']);

        if ($text_color || $link_color || $bg_color) {
            echo '<style type="text/css">';

            if ($bg_color) {
                echo '.floating-toc { background-color: ' . $bg_color . ' !important; }';
            }

            if ($text_color) {
                echo '.toc-heading, .toc-toggle { color: ' . $text_color . ' !important; }';
            }

            if ($link_color) {
                echo '.toc-content a { color: ' . $link_color . ' !important; }';
                echo '.toc-content a:hover, .toc-content a.active { color: ' . $link_color . ' !important; }';
            }

            echo '</style>';
        }
    }
    
    public static function generate_heading_id($text) {
        // Remove HTML tags
        $text = strip_tags($text);
        
        // Convert to lowercase and replace spaces with hyphens
        $id = strtolower($text);
        $id = preg_replace('/[^a-z0-9\s-]/', '', $id);
        $id = preg_replace('/\s+/', '-', $id);
        $id = trim($id, '-');
        
        // Limit length
        $id = substr($id, 0, 50);
        
        return $id;
    }
    
    public function get_toc_data() {
        $content = $this->get_post_content();
        $headings = $this->extract_headings($content);
        
        return array(
            'headings' => $headings,
            'label' => $this->options['label'],
            'has_content' => !empty($headings)
        );
    }
}
?>