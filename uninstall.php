'ID'));
    foreach ($users as $user_id) {
        delete_user_meta($user_id, 'safeoid_toc_preferences');
    }
    
    // Remove any custom post meta (if any were stored)
    global $wpdb;
    
    $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE 'safeoid_toc_%'");
    
    // Remove any custom tables (if any were created)
    // Note: This plugin doesn't create custom tables, but this is here for reference
    // $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}safeoid_toc_data");
    
    // Clear any cron jobs (if any were scheduled)
    wp_clear_scheduled_hook('safeoid_toc_cleanup_cron');
    
    // Log the uninstall for debugging (optional)
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('Safeoid TOC Plugin: Uninstall completed successfully');
    }
}

// Perform the cleanup
safeoid_toc_cleanup();

/**
 * Multisite cleanup
 */
if (is_multisite()) {
    global $wpdb;
    
    $blog_ids = $wpdb->get_col("SELECT blog_id FROM {$wpdb->blogs}");
    $original_blog_id = get_current_blog_id();
    
    foreach ($blog_ids as $blog_id) {
        switch_to_blog($blog_id);
        safeoid_toc_cleanup();
    }
    
    switch_to_blog($original_blog_id);
    
    // Clean up network-wide options if any
    delete_site_option('safeoid_toc_network_options');
}

// Final cleanup message
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('Safeoid TOC Plugin: All data removed successfully');
}
?>