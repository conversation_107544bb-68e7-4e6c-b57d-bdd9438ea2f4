/* Safeoid TOC Styles */
.floating-toc {
    position: fixed;
    top: 20%;
    left: 20px;
    width: 280px;
    max-height: 60vh;
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
    overflow: hidden;
}

.floating-toc.hidden {
    transform: translateX(-100%);
    opacity: 0;
}

.toc-heading {
    margin: 0;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.toc-toggle {
    display: none;
    padding: 12px 20px;
    background: #007cba;
    color: white;
    cursor: pointer;
    font-weight: 500;
    border-radius: 6px 6px 0 0;
    transition: background-color 0.2s ease;
}

.toc-toggle:hover {
    background: #005a87;
}

.toc-content {
    padding: 0;
    max-height: calc(60vh - 60px);
    overflow-y: auto;
}

.toc-content ul {
    margin: 0;
    padding: 10px 0;
    list-style: none;
}

.toc-content li {
    margin: 0;
    padding: 0;
}

.toc-content a {
    display: block;
    padding: 8px 20px;
    text-decoration: none;
    color: #495057;
    font-size: 14px;
    line-height: 1.4;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.toc-content a:hover {
    background: #f8f9fa;
    color: #007cba;
    border-left-color: #007cba;
}

.toc-content a.active {
    background: #e3f2fd;
    color: #1976d2;
    border-left-color: #1976d2;
    font-weight: 500;
}

/* Nested headings */
.toc-content ul ul a {
    padding-left: 40px;
    font-size: 13px;
}

.toc-content ul ul ul a {
    padding-left: 60px;
    font-size: 12px;
}

.toc-content ul ul ul ul a {
    padding-left: 80px;
}

/* Scrollbar styling */
.toc-content::-webkit-scrollbar {
    width: 6px;
}

.toc-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.toc-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.toc-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Desktop specific */
@media (min-width: 769px) {
    body.has-toc {
        padding-left: 320px;
        transition: padding-left 0.3s ease;
    }
    
    .floating-toc {
        display: block;
    }
    
    .toc-toggle {
        display: none;
    }
}

/* Mobile and tablet */
@media (max-width: 768px) {
    .floating-toc {
        position: sticky;
        top: 0;
        left: 0;
        width: 100%;
        max-height: none;
        border-radius: 0;
        border-left: none;
        border-right: none;
        transform: none;
        opacity: 1;
    }
    
    .toc-toggle {
        display: block;
    }
    
    .toc-content {
        display: none;
        max-height: 300px;
        border-top: 1px solid #e9ecef;
    }
    
    .toc-content.active {
        display: block;
    }
    
    .toc-heading {
        display: none;
    }
    
    body.has-toc {
        padding-left: 0;
    }
}

/* Content spacing adjustments */
.floating-toc + .content,
.floating-toc + main,
.floating-toc + article {
    margin-top: 20px;
}

/* Animation for smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.toc-content a:focus {
    outline: 2px solid #007cba;
    outline-offset: -2px;
    background: #e3f2fd;
}

.toc-toggle:focus {
    outline: 2px solid #ffffff;
    outline-offset: -2px;
}

/* Print styles */
@media print {
    .floating-toc {
        display: none;
    }

    body.has-toc {
        padding-left: 0;
    }
}