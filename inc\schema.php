<?php
/**
 * Schema Markup Handler
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOC_Schema {

    private $options;

    public function __construct($options) {
        $this->options = $options;
    }
    
    public function output_schema() {
        global $post;
        
        if (!$post || !$this->options['enable_schema']) {
            return;
        }
        
        $toc_output = new SafeoidTOC_Output($this->options);
        $toc_data = $toc_output->get_toc_data();
        
        if (empty($toc_data['headings'])) {
            return;
        }
        
        $schema = $this->generate_schema($toc_data['headings']);
        
        if ($schema) {
            echo '<script type="application/ld+json">';
            echo wp_json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            echo '</script>';
        }
    }
    
    private function generate_schema($headings) {
        global $post;
        
        if (empty($headings)) {
            return null;
        }
        
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'mainEntityOfPage' => array(
                '@type' => 'WebPage',
                '@id' => get_permalink($post->ID)
            ),
            'headline' => get_the_title($post->ID),
            'description' => $this->get_post_excerpt(),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', $post->post_author)
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => $this->get_site_logo()
                )
            ),
            'datePublished' => get_the_date('c', $post->ID),
            'dateModified' => get_the_modified_date('c', $post->ID)
        );
        
        // Add Table of Contents structure
        $toc_items = $this->build_toc_schema($headings);
        if (!empty($toc_items)) {
            $schema['hasPart'] = array(
                '@type' => 'Table',
                'about' => __('Table of Contents', 'safeoid-toc'),
                'hasPart' => $toc_items
            );
        }
        
        return $schema;
    }
    
    private function build_toc_schema($headings) {
        $items = array();
        $base_url = get_permalink();
        
        foreach ($headings as $index => $heading) {
            $id = SafeoidTOC_Output::generate_heading_id($heading['text']);
            
            $items[] = array(
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $heading['text'],
                'url' => $base_url . '#' . $id,
                'item' => array(
                    '@type' => 'WebPageElement',
                    '@id' => $base_url . '#' . $id,
                    'name' => $heading['text'],
                    'description' => $this->truncate_text($heading['text'], 100)
                )
            );
        }
        
        return $items;
    }
    
    private function get_post_excerpt() {
        global $post;
        
        if (!empty($post->post_excerpt)) {
            return wp_strip_all_tags($post->post_excerpt);
        }
        
        $content = wp_strip_all_tags($post->post_content);
        return $this->truncate_text($content, 160);
    }
    
    private function get_site_logo() {
        $custom_logo_id = get_theme_mod('custom_logo');
        
        if ($custom_logo_id) {
            $logo = wp_get_attachment_image_src($custom_logo_id, 'full');
            if ($logo) {
                return $logo[0];
            }
        }
        
        // Fallback to site icon
        $site_icon = get_site_icon_url();
        if ($site_icon) {
            return $site_icon;
        }
        
        // Final fallback
        return get_template_directory_uri() . '/assets/images/logo.png';
    }
    
    private function truncate_text($text, $length = 160) {
        $text = wp_strip_all_tags($text);
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        if (strlen($text) <= $length) {
            return $text;
        }
        
        $truncated = substr($text, 0, $length);
        $last_space = strrpos($truncated, ' ');
        
        if ($last_space !== false) {
            $truncated = substr($truncated, 0, $last_space);
        }
        
        return $truncated . '...';
    }
    
    public function get_schema_data() {
        global $post;
        
        if (!$post) {
            return null;
        }
        
        $toc_output = new SafeoidTOC_Output($this->options);
        $toc_data = $toc_output->get_toc_data();
        
        return array(
            'enabled' => $this->options['enable_schema'],
            'has_content' => !empty($toc_data['headings']),
            'headings_count' => count($toc_data['headings'] ?? array()),
            'post_title' => get_the_title($post->ID),
            'post_url' => get_permalink($post->ID)
        );
    }
}
?>