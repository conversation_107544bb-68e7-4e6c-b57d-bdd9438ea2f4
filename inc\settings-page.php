options = $options;
    }
    
    public function render() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        $this->render_settings_page();
    }
    
    private function save_settings() {
        if (!wp_verify_nonce($_POST['safeoid_toc_nonce'], 'safeoid_toc_settings')) {
            wp_die(__('Security check failed', 'safeoid-toc'));
        }
        
        $new_options = array();
        
        // Headings selection
        $new_options['headings'] = isset($_POST['headings']) ? 
            array_map('sanitize_text_field', $_POST['headings']) : 
            array('h2', 'h3');
        
        // TOC label
        $new_options['label'] = isset($_POST['label']) ? 
            sanitize_text_field($_POST['label']) : 
            __('Table of Contents', 'safeoid-toc');
        
        // Display options
        $new_options['enable_desktop'] = isset($_POST['enable_desktop']);
        $new_options['enable_mobile'] = isset($_POST['enable_mobile']);
        $new_options['enable_schema'] = isset($_POST['enable_schema']);
        
        // Colors
        $new_options['text_color'] = sanitize_hex_color($_POST['text_color'] ?? '#333333');
        $new_options['link_color'] = sanitize_hex_color($_POST['link_color'] ?? '#0073aa');
        $new_options['bg_color'] = sanitize_hex_color($_POST['bg_color'] ?? '#ffffff');
        
        update_option('safeoid_toc_options', $new_options);
        $this->options = $new_options;
        
        echo '' . 
             __('Settings saved successfully!', 'safeoid-toc') . 
             '';
    }
    
    private function render_settings_page() {
        ?>
        
            
            
            
                
                    
                        
                        
                        
                            
                                
                                    
                                
                                
                                    render_heading_checkboxes(); ?>
                                    
                                        
                                    
                                
                            
                            
                            
                                
                                    
                                
                                
                                    
                                    
                                        
                                    
                                
                            
                            
                            
                                
                                    
                                
                                
                                    
                                        
                                            options['enable_desktop']); ?> />
                                            
                                        
                                        
                                        
                                            options['enable_mobile']); ?> />
                                            
                                        
                                        
                                        
                                            options['enable_schema']); ?> />
                                            
                                        
                                    
                                
                            
                            
                            
                                
                                    
                                
                                
                                    render_color_options(); ?>
                                
                            
                        
                        
                        
                    
                
                
                
                    render_sidebar(); ?>
                
            
        
        
        
        .safeoid-toc-admin {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }
        .safeoid-toc-main {
            flex: 2;
        }
        .safeoid-toc-sidebar {
            flex: 1;
        }
        .safeoid-info-box {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .safeoid-info-box h3 {
            margin-top: 0;
            color: #23282d;
        }
        .color-picker-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .color-picker {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        
        options['headings'];
        
        echo '';
        foreach ($headings as $heading) {
            $checked = in_array($heading, $selected) ? 'checked' : '';
            echo '';
            echo '';
            echo ' ' . strtoupper($heading);
            echo '';
        }
        echo '';
    }
    
    private function render_color_options() {
        $colors = array(
            'text_color' => __('Text Color', 'safeoid-toc'),
            'link_color' => __('Link Color', 'safeoid-toc'),
            'bg_color' => __('Background Color', 'safeoid-toc')
        );
        
        foreach ($colors as $key => $label) {
            $value = $this->options[$key] ?? '#ffffff';
            echo '';
            echo '';
            echo '' . $label . '';
            echo '';
        }
    }
    
    private function render_sidebar() {
        ?>
        
            
            
                
            
            
                ✅ 
                ✅ 
                ✅ 
                ✅ 
                ✅ 
            
        
        
        
            
            
                
            
            
                
                
            
            
                
                
            
        
        
        
            
            
                
            
            
                
                    
                
            
        
        