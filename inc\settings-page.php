<?php
/**
 * Settings Page Handler
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SafeoidTOC_Settings {

    private $options;

    public function __construct($options) {
        $this->options = $options;
    }
    
    public function render() {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }
        
        $this->render_settings_page();
    }
    
    private function save_settings() {
        if (!wp_verify_nonce($_POST['safeoid_toc_nonce'], 'safeoid_toc_settings')) {
            wp_die(__('Security check failed', 'safeoid-toc'));
        }
        
        $new_options = array();
        
        // Headings selection
        $new_options['headings'] = isset($_POST['headings']) ? 
            array_map('sanitize_text_field', $_POST['headings']) : 
            array('h2', 'h3');
        
        // TOC label
        $new_options['label'] = isset($_POST['label']) ? 
            sanitize_text_field($_POST['label']) : 
            __('Table of Contents', 'safeoid-toc');
        
        // Display options
        $new_options['enable_desktop'] = isset($_POST['enable_desktop']);
        $new_options['enable_mobile'] = isset($_POST['enable_mobile']);
        $new_options['enable_schema'] = isset($_POST['enable_schema']);
        
        // Colors
        $new_options['text_color'] = sanitize_hex_color($_POST['text_color'] ?? '#333333');
        $new_options['link_color'] = sanitize_hex_color($_POST['link_color'] ?? '#0073aa');
        $new_options['bg_color'] = sanitize_hex_color($_POST['bg_color'] ?? '#ffffff');
        
        update_option('safeoid_toc_options', $new_options);
        $this->options = $new_options;
        
        echo '<div class="notice notice-success is-dismissible">' .
             __('Settings saved successfully!', 'safeoid-toc') .
             '</div>';
    }
    
    private function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('TOC Settings', 'safeoid-toc'); ?></h1>

            <div class="safeoid-toc-admin">
                <div class="safeoid-toc-main">
                    <form method="post" action="">
                        <?php wp_nonce_field('safeoid_toc_settings', 'safeoid_toc_nonce'); ?>

                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Include Headings', 'safeoid-toc'); ?></label>
                                </th>
                                <td>
                                    <?php $this->render_heading_checkboxes(); ?>
                                    <p class="description">
                                        <?php _e('Select which heading levels to include in the table of contents.', 'safeoid-toc'); ?>
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label for="label"><?php _e('TOC Label', 'safeoid-toc'); ?></label>
                                </th>
                                <td>
                                    <input type="text" id="label" name="label" value="<?php echo esc_attr($this->options['label']); ?>" class="regular-text" />
                                    <p class="description">
                                        <?php _e('The heading text for the table of contents.', 'safeoid-toc'); ?>
                                    </p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label><?php _e('Display Options', 'safeoid-toc'); ?></label>
                                </th>
                                <td>
                                    <fieldset>
                                        <label>
                                            <input type="checkbox" name="enable_desktop" value="1" <?php checked($this->options['enable_desktop']); ?> />
                                            <?php _e('Enable on Desktop', 'safeoid-toc'); ?>
                                        </label>
                                        <br>
                                        <label>
                                            <input type="checkbox" name="enable_mobile" value="1" <?php checked($this->options['enable_mobile']); ?> />
                                            <?php _e('Enable on Mobile', 'safeoid-toc'); ?>
                                        </label>
                                        <br>
                                        <label>
                                            <input type="checkbox" name="enable_schema" value="1" <?php checked($this->options['enable_schema']); ?> />
                                            <?php _e('Enable Schema Markup', 'safeoid-toc'); ?>
                                        </label>
                                    </fieldset>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row">
                                    <label><?php _e('Colors', 'safeoid-toc'); ?></label>
                                </th>
                                <td>
                                    <?php $this->render_color_options(); ?>
                                </td>
                            </tr>
                        </table>

                        <?php submit_button(); ?>
                    </form>
                </div>

                <div class="safeoid-toc-sidebar">
                    <?php $this->render_sidebar(); ?>
                </div>
            </div>
        </div>
        <style>
        .safeoid-toc-admin {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }
        .safeoid-toc-main {
            flex: 2;
        }
        .safeoid-toc-sidebar {
            flex: 1;
        }
        .safeoid-info-box {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .safeoid-info-box h3 {
            margin-top: 0;
            color: #23282d;
        }
        .color-picker-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .color-picker {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        </style>
        <?php
    }

    private function render_heading_checkboxes() {
        $headings = array('h1', 'h2', 'h3', 'h4', 'h5', 'h6');
        $selected = $this->options['headings'];

        echo '<fieldset>';
        foreach ($headings as $heading) {
            $checked = in_array($heading, $selected) ? 'checked' : '';
            echo '<label>';
            echo '<input type="checkbox" name="headings[]" value="' . $heading . '" ' . $checked . ' />';
            echo ' ' . strtoupper($heading);
            echo '</label><br>';
        }
        echo '</fieldset>';
    }
    
    private function render_color_options() {
        $colors = array(
            'text_color' => __('Text Color', 'safeoid-toc'),
            'link_color' => __('Link Color', 'safeoid-toc'),
            'bg_color' => __('Background Color', 'safeoid-toc')
        );

        foreach ($colors as $key => $label) {
            $value = $this->options[$key] ?? '#ffffff';
            echo '<div class="color-picker-wrapper">';
            echo '<input type="color" name="' . $key . '" value="' . esc_attr($value) . '" class="color-picker" />';
            echo '<label for="' . $key . '">' . $label . '</label>';
            echo '</div>';
        }
    }

    private function render_sidebar() {
        ?>
        <div class="safeoid-info-box">
            <h3><?php _e('Features', 'safeoid-toc'); ?></h3>
            <ul>
                <li>✅ <?php _e('Floating Table of Contents', 'safeoid-toc'); ?></li>
                <li>✅ <?php _e('Elementor Widget', 'safeoid-toc'); ?></li>
                <li>✅ <?php _e('Schema Markup Support', 'safeoid-toc'); ?></li>
                <li>✅ <?php _e('Mobile Friendly', 'safeoid-toc'); ?></li>
                <li>✅ <?php _e('Customizable Colors', 'safeoid-toc'); ?></li>
            </ul>
        </div>

        <div class="safeoid-info-box">
            <h3><?php _e('Need Help?', 'safeoid-toc'); ?></h3>
            <p>
                <?php _e('Check out our documentation for help with setup and customization.', 'safeoid-toc'); ?>
            </p>
            <p>
                <a href="https://safeoid.com/docs/toc-plugin/" class="button button-primary" target="_blank">
                    <?php _e('View Documentation', 'safeoid-toc'); ?>
                </a>
            </p>
        </div>

        <div class="safeoid-info-box">
            <h3><?php _e('Rate Our Plugin', 'safeoid-toc'); ?></h3>
            <p>
                <?php _e('If you enjoy using our plugin, please take a moment to rate it on WordPress.org', 'safeoid-toc'); ?>
            </p>
            <p>
                <a href="https://wordpress.org/plugins/safeoid-toc/reviews/" class="button" target="_blank">
                    <?php _e('Leave a Review', 'safeoid-toc'); ?>
                </a>
            </p>
        </div>
        <?php
    }